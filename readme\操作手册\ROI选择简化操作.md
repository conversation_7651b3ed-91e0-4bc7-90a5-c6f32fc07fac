# LED数码管检测系统 - 快速上手指南

## 🚀 三步快速开始

### 第一步：摄像头设置 (30秒)
```
启动程序 → 调整参数 → 保存设置 → 进入校准
```

**核心快捷键**：
- `T`/`t` - 切换分辨率
- `E`/`e` - 调整曝光 (±1.0)
- `B`/`b` - 调整亮度 (±10.0)
- `S` - 保存设置
- `Enter` - 进入校准

---

### 第二步：校准配置 (5分钟)

#### 校准主菜单
```
'B' 基准点 | 'L' LED校准 | 'D' 数码管校准 | 'Enter' 开始检测
```

#### LED校准流程 (推荐)
```
1. 按 'L' → 2. 拖拽选择ROI → 3. 采集样本 → 4. 自动分析
```

**ROI选择快捷键**：
- **拖拽** - 选择区域
- `Enter` - 确认ROI
- `N` - 跳过当前
- `B` - 回退上一个 🆕
- `T` - 模板复制 🆕

**样本采集**：
- 关闭所有LED → `C` 采集灭灯样本
- 点亮所有LED → `C` 采集亮灯样本
- `A` - 自动分析阈值

---

### 第三步：实时检测 (开始使用)
```
校准完成 → 按 Enter → 开始实时检测
```

**检测模式快捷键**：
- `g`/`G` - 绿LED灰度阈值
- `v`/`V` - 绿LED绿色阈值
- `y`/`Y` - 红LED灰度阈值
- `r`/`R` - 红LED红色阈值
- `+`/`-` - 数码管亮度阈值
- `S` - 保存所有阈值 🆕
- `B` - 切换基准点对齐 🆕

---

## ⚡ 新功能快速使用

### 🎯 基准点自动对齐 (推荐)
```
校准菜单按 'B' → 点击2个特征点 → Enter确认 → 自动对齐ROI
```
**优势**: 产品位置偏移时自动调整ROI坐标

### 📋 模板复制功能
```
选择第一个ROI → 按 'T' → 点击位置快速复制 → Enter确认
```
**优势**: 快速创建相同尺寸的ROI

### ✏️ ROI编辑模式
```
校准菜单按 'E' → 点击或按数字键选择ROI → 拖拽调整 → Enter保存
```
**优势**: 后期微调ROI位置，无需重新校准

### 🔄 快速重新采样
```
校准菜单按 'R' → 采集新样本 → 自动更新阈值
```
**优势**: 光照变化时快速更新检测参数

---

## 🎮 核心快捷键速查

### 通用操作
| 按键 | 功能 | 使用场景 |
|------|------|----------|
| `Enter` | 确认/进入下一步 | 所有模式 |
| `Esc` | 返回/退出 | 所有模式 |
| `Q` | 退出程序 | 任何时候 |

### 摄像头设置
| 按键 | 功能 | 说明 |
|------|------|------|
| `T`/`t` | 分辨率 | 循环切换 |
| `E`/`e` | 曝光 | 大写+，小写- |
| `B`/`b` | 亮度 | 大写+，小写- |
| `S` | 保存 | 保存摄像头参数 |

### 校准模式
| 按键 | 功能 | 说明 |
|------|------|------|
| `B` | 基准点校准 | 🆕 自动对齐 |
| `L` | LED校准 | 完整流程 |
| `D` | 数码管校准 | 完整流程 |
| `E` | 编辑LED | 🆕 调整位置 |
| `R` | 重新采样 | 🆕 快速更新 |

### ROI选择
| 按键 | 功能 | 说明 |
|------|------|------|
| **拖拽** | 选择区域 | 鼠标操作 |
| `Enter` | 确认ROI | 保存当前 |
| `N` | 跳过 | 不设置检测 |
| `B` | 回退 | 🆕 重选上一个 |
| `T` | 模板模式 | 🆕 复制尺寸 |
| `R` | 重置全部 | 清空重来 |

### WASD微调 🆕
| 按键 | 功能 | 步长 |
|------|------|------|
| `W`/`S` | 上下移动 | 1像素 |
| `A`/`D` | 左右移动 | 1像素 |
| `Shift`+WASD | 大步长 | 10像素 |
| `Q`/`E` | 调整宽度 | ±1像素 |
| `Z`/`C` | 调整高度 | ±1像素 |

### 检测模式
| 按键 | 功能 | 调整幅度 |
|------|------|----------|
| `g`/`G` | 绿LED灰度 | ±5.0 |
| `v`/`V` | 绿LED绿色 | ±5.0 |
| `y`/`Y` | 红LED灰度 | ±5.0 |
| `r`/`R` | 红LED红色 | ±5.0 |
| `+`/`-` | 数码管亮度 | ±1.0 |
| `S` | 保存全部 | 🆕 完整保存 |
| `L` | 保存LED | 🆕 快速保存 |
| `B` | 基准点对齐 | 🆕 开关切换 |
| `C` | 返回校准 | 重新设置 |

---

## 🔧 常见操作场景

### 场景1：首次使用
```
1. 调整摄像头参数 → S保存 → Enter
2. 按L进行LED校准 → 拖拽选择ROI → Enter确认
3. 关闭LED → C采集 → 点亮LED → C采集 → A分析
4. Enter进入检测模式
```

### 场景2：ROI位置需要调整
```
检测模式按C → 校准菜单按E → 点击ROI → 拖拽调整 → Enter保存
```

### 场景3：光照条件变化
```
检测模式按C → 校准菜单按R → 重新采集样本 → 自动更新阈值
```

### 场景4：检测效果不佳
```
检测模式下：
- 调整LED阈值：g/G, v/V, y/Y, r/R
- 调整数码管阈值：+/-
- 保存优化结果：S
```

### 场景5：产品位置偏移
```
校准时设置基准点：B → 点击2个特征点 → Enter
检测时自动对齐：B键切换开关
```

---

## 🎯 快速故障排除

### 问题：摄像头图像不清晰
**解决**：调整曝光(E/e)和亮度(B/b)参数

### 问题：ROI选择不准确
**解决**：使用WASD微调功能精确调整

### 问题：LED检测错误
**解决**：检测模式下调整阈值(g/G, v/V, y/Y, r/R)

### 问题：产品位置变化影响检测
**解决**：使用基准点自动对齐功能(B键)

### 问题：配置丢失
**解决**：删除combined_config.json重新校准

---

## 📋 快速检查清单

### ✅ 启动前检查
- [ ] 摄像头连接正常
- [ ] 光照条件稳定
- [ ] 产品位置固定

### ✅ 校准检查
- [ ] 摄像头参数调整完成
- [ ] LED ROI选择完成
- [ ] 样本采集成功
- [ ] 阈值分析完成

### ✅ 检测检查
- [ ] LED状态显示正确
- [ ] 数码管识别正确
- [ ] 基准点对齐正常(如启用)

---

## 💡 使用技巧

### 🎯 提高效率
- 使用模板复制功能快速选择相似ROI
- 使用数字键(1-6)快速选择LED进行编辑
- 使用快速重新采样应对光照变化

### 🔧 提高精度
- 使用WASD微调实现像素级精确定位
- 设置基准点实现自动对齐补偿
- 定期保存优化后的阈值参数

### 🛡️ 避免问题
- 校准时确保LED状态完全切换
- 保持稳定的光照环境
- 定期备份配置文件

---

**版本**: v2.0 简化版  
**更新**: 2025-01-13  

💡 **提示**: 详细功能说明请参考《ROI选择V2.md》完整文档

🆕 **新功能**: 基准点对齐 | WASD微调 | 模板复制 | ROI编辑 | 快速重新采样
