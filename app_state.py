import numpy as np
import cv2
from constants import * # 导入所有常量
from collections import deque
import logging # Import logging

class AppState:
    """管理应用程序的共享状态"""
    def __init__(self):
        # --- 程序状态 ---
        self.current_mode = MODE_CAMERA_SETTINGS # 初始模式
        self.current_calib_state = CALIB_STATE_START # 校准模式下的子状态
        self.running = True
        self.cap = None # 摄像头对象 (VideoCapture)
        self.current_frame = None # 当前捕获的原始帧
        self.display_frame = None # 用于显示的帧 (可能包含绘制)

        # --- 摄像头参数 ---
        # 这些值会被 config_manager.load_config 更新
        self.active_cam_idx = DEFAULT_CAMERA_INDEX
        self.current_resolution_index = DEFAULT_RESOLUTION_INDEX
        self.exposure_value = DEFAULT_EXPOSURE
        self.brightness_value = DEFAULT_BRIGHTNESS

        # --- LED 检测相关 ---
        # 这些值会被 config_manager.load_config 更新或初始化
        self.led_num_green = DEFAULT_NUM_GREEN_LEDS
        self.led_num_red = DEFAULT_NUM_RED_LEDS
        self.led_max_rois = self.led_num_green + self.led_num_red
        self.led_rois = [None] * self.led_max_rois
        self.led_off_state_samples = [[] for _ in range(self.led_max_rois)]
        self.led_on_state_samples = [[] for _ in range(self.led_max_rois)]
        self.led_gray_threshold_green = DEFAULT_LED_GRAY_THRESHOLD_GREEN
        self.led_green_threshold = DEFAULT_LED_GREEN_THRESHOLD
        self.led_gray_threshold_red = DEFAULT_LED_GRAY_THRESHOLD_RED
        self.led_red_threshold = DEFAULT_LED_RED_THRESHOLD

        # --- 数码管检测相关 ---
        # 这些值会被 config_manager.load_config 更新
        self.digit_rois = [None] * NUM_DIGITS
        self.digit_segment_rois = [[None] * NUM_SEGMENTS_PER_DIGIT for _ in range(NUM_DIGITS)]
        self.digit_brightness_threshold = DEFAULT_DIGIT_BRIGHTNESS_THRESHOLD
        # 校准过程中使用的临时图像
        self.digit_calibration_image_88 = None
        self.digit_background_image_off = None

        # --- UI & 校准辅助 ---
        self.selecting_roi = False
        self.roi_start_point = (-1, -1)
        self.current_rect = None # 临时绘制的矩形 (x, y, w, h)
        self.calib_led_roi_index = 0 # 当前选择/操作的 LED ROI 索引
        self.calib_digit_index = 0 # 当前校准的数码管索引 (0 或 1)
        self.calib_segment_index = 0 # 当前校准的数码管段索引 (0-6)
        self.prompt_message = "" # UI 提示信息
        self.status_message = "" # UI 状态信息


        # --- ROI 微调 ---
        self.fine_tune_step = 1  # ROI微调的默认步长(像素)

        # --- 模板复制功能 ---
        self.template_roi = None # 存储模板ROI的尺寸 (w, h)
        self.template_mode = False # 是否在模板复制模式
        self.template_preview_pos = None # 模板预览位置 (x, y, w, h)

        # --- 固定尺寸模板功能 ---
        self.fixed_template_size = DEFAULT_FIXED_TEMPLATE_SIZE # 固定模板大小（像素）
        self.fixed_template_mode = False # 是否在固定尺寸模板模式

        # --- ROI编辑功能 ---
        self.edit_mode = False # 是否在编辑模式
        self.selected_roi_index = -1 # 当前选中的ROI索引 (-1表示未选中)
        self.moving_roi = False # 是否正在移动ROI
        self.move_start_pos = None # 移动开始时的鼠标位置
        self.original_roi_pos = None # ROI的原始位置

        # --- 基准点对齐系统 ---
        # 基准点坐标和模板
        self.base_points = [None, None]  # 当前基准点位置 [(x1,y1), (x2,y2)]
        self.base_templates = [None, None]  # 基准点模板图像 [template1, template2]
        self.original_base_points = [None, None]  # 校准时的原始基准点位置

        # 原始ROI坐标（用于计算偏移）
        self.original_led_rois = [None] * self.led_max_rois
        self.original_digit_rois = [None] * NUM_DIGITS
        self.original_digit_segment_rois = [[None] * NUM_SEGMENTS_PER_DIGIT for _ in range(NUM_DIGITS)]

        # 对齐控制参数
        self.alignment_enabled = True  # 是否启用动态对齐
        self.base_template_size = BASE_TEMPLATE_SIZE  # 模板尺寸
        self.base_match_threshold = BASE_MATCH_THRESHOLD  # 匹配阈值
        self.alignment_fail_count = 0  # 连续失败计数
        self.last_alignment_success = False  # 上次对齐是否成功

        # 校准状态
        self.calib_base_point_index = 0  # 当前选择的基准点索引(0或1)
        self.is_quick_resample_mode = False  # 是否为快速重新采样模式（保留ROI，只更新样本）
        self.is_quick_resample_mode = False  # 是否为快速重新采样模式（保留ROI，只更新样本）

        # --- 检测结果 (在检测模式下更新) ---
        self.led_last_status = [False] * self.led_max_rois
        self.led_last_values = [(0.0, 0.0, 0.0)] * self.led_max_rois
        self.digit_last_recognized_chars = [None] * NUM_DIGITS
        self.digit_last_segment_patterns = [[0] * NUM_SEGMENTS_PER_DIGIT for _ in range(NUM_DIGITS)]
        self.digit_last_missing_segments = [[] for _ in range(NUM_DIGITS)]

        # --- FPS 计算 ---
        self.prev_time = 0.0
        self.fps = 0.0

        # --- LED 状态历史与时间戳 (用于增强稳定性) ---
        self.led_history = [deque(maxlen=LED_HISTORY_SIZE) for _ in range(self.led_max_rois)] # 存储 (状态, (灰,绿,红)) 元组
        self.led_last_stable_status = [False] * self.led_max_rois # 存储过滤后的稳定状态
        self.led_last_change_time = [0.0] * self.led_max_rois # 上次稳定状态变化的时间

        # --- New states for "88" detection workflow ---
        self.led_analysis_state = 'IDLE'  # Possible states: 'IDLE', 'LOGGING', 'WAITING_TO_SIGNAL_12', 'ANALYZING', 'SENDING_RESULT_10', 'SENDING_RESULT_13', 'CLEARING'
        self.logging_start_time = None    # Timestamp when logging for analysis started
        self.logging_duration = DEFAULT_LOGGING_DURATION  # Configurable logging duration in seconds
        self.last_analysis_result = None  # Stores the number of perfect cycles found by analyze_led_log.py
        self.last_special_leds_result = None  # Stores the G33/R1/R2 monitoring result ('GOOD' or 'BAD')
        self.log_file_handler = None      # Store the file handler instance for dynamic add/remove

        # --- 异步任务管理相关状态 ---
        self.current_analysis_task_id = None    # 当前分析任务的ID
        self.current_cpu_task_id = None         # 当前CPU通信任务的ID
        self.async_task_progress = ""           # 异步任务进度信息（用于UI显示）
        self.last_task_cleanup_time = 0.0       # 上次清理任务的时间

        # === 第一阶段精度优化相关 ===
        self.ambient_roi = None                    # 背景参考ROI（预留）
        self.ambient_gray_ref = 0.0               # 采样时的背景灰度（预留）
        self.brightness_k = 1.0                   # 当前亮度系数（预留）
        self.last_k_update_time = 0.0            # 上次更新K值的时间（预留）

    def update_led_counts(self, num_green, num_red):
        """当 LED 数量改变时，更新相关状态列表的大小"""
        if num_green < 0: num_green = 0
        if num_red < 0: num_red = 0

        new_max_rois = num_green + num_red
        if new_max_rois != self.led_max_rois:
            print(f"Updating LED counts: Green={num_green}, Red={num_red}, Total={new_max_rois}")
            self.led_num_green = num_green
            self.led_num_red = num_red
            old_max_rois = self.led_max_rois
            self.led_max_rois = new_max_rois

            # 调整所有相关列表
            lists_to_adjust = [
                'led_rois', 'led_off_state_samples', 'led_on_state_samples',
                'led_last_status', 'led_last_values',
                'led_history', 'led_last_stable_status', 'led_last_change_time',
                'original_led_rois'  # 新增原始ROI列表
            ]
            default_values = {
                'led_rois': None,
                'led_off_state_samples': [],
                'led_on_state_samples': [],
                'led_last_status': False,
                'led_last_values': (0.0, 0.0, 0.0),
                'led_history': deque(maxlen=LED_HISTORY_SIZE),
                'led_last_stable_status': False,
                'led_last_change_time': 0.0,
                'original_led_rois': None  # 新增原始ROI默认值
            }

            for list_name in lists_to_adjust:
                current_list = getattr(self, list_name)
                default_val = default_values[list_name]
                if new_max_rois > old_max_rois:
                    # 对于需要独立实例的列表（如 history 的内层列表），特殊处理
                    if list_name == 'led_history':
                         current_list.extend([deque(maxlen=LED_HISTORY_SIZE) for _ in range(new_max_rois - old_max_rois)])
                    elif list_name in ['led_off_state_samples', 'led_on_state_samples']:
                         current_list.extend([[] for _ in range(new_max_rois - old_max_rois)])
                    else:
                         current_list.extend([default_val] * (new_max_rois - old_max_rois))
                elif new_max_rois < old_max_rois:
                    setattr(self, list_name, current_list[:new_max_rois])

            # 重置校准索引
            self.calib_led_roi_index = 0
            # 清空历史记录和时间戳，因为 LED 对应关系变了
            self.led_history = [deque(maxlen=LED_HISTORY_SIZE) for _ in range(self.led_max_rois)]
            self.led_last_stable_status = [False] * self.led_max_rois
            self.led_last_change_time = [0.0] * self.led_max_rois
            # 清空样本数据，因为需要重新校准
            self.led_off_state_samples = [[] for _ in range(self.led_max_rois)]
            self.led_on_state_samples = [[] for _ in range(self.led_max_rois)]
            # 重置原始ROI列表
            self.original_led_rois = [None] * self.led_max_rois

        elif self.led_num_green != num_green or self.led_num_red != num_red:
             # 总数没变，但绿红比例变了
             self.led_num_green = num_green
             self.led_num_red = num_red
             print(f"LED type distribution updated: Green={num_green}, Red={num_red}")
             # 比例变化也应重置历史和时间戳，并清空样本
             self.led_history = [deque(maxlen=LED_HISTORY_SIZE) for _ in range(self.led_max_rois)]
             self.led_last_stable_status = [False] * self.led_max_rois
             self.led_last_change_time = [0.0] * self.led_max_rois
             self.led_off_state_samples = [[] for _ in range(self.led_max_rois)]
             self.led_on_state_samples = [[] for _ in range(self.led_max_rois)]
             # 重置原始ROI列表
             self.original_led_rois = [None] * self.led_max_rois
             print("LED history, status timestamps, and samples cleared due to type distribution change.")
             # 可能需要重置校准或阈值，这里暂时不处理，由后续逻辑决定


# 可以在这里创建一个全局实例供其他模块导入和使用
# app_state = AppState()
# 或者在使用时创建实例并传递
