import cv2
import time
import logging
import os # 添加 os 导入

from app_state import AppState
from constants import *
import config_manager
import camera_manager
import ui_handler # Import the UI handler
from constants import LOG_FILE # <--- 显式导入 LOG_FILE
import async_task_manager # <--- 导入异步任务管理器
import async_task_manager # <--- 导入异步任务管理器

# --- 日志设置 --- Constants
# LOG_FILE = 'led_digit_detection.log' # <--- 移除这里的定义
LOG_LEVEL = logging.INFO  # 改回INFO，以提高生产环境稳定性
LOG_FORMAT = '%(asctime)s.%(msecs)03d - %(levelname)s - %(message)s'
LOG_DATE_FORMAT = '%Y-%m-%d %H:%M:%S'

def setup_logging(app_state: AppState):
    """配置日志记录，使用 FileHandler 并将其存储在 AppState 中。"""
    # 获取根 logger
    logger = logging.getLogger()
    logger.setLevel(LOG_LEVEL)

    # --- 尝试移除根 logger 可能存在的旧 handlers --- #
    # 这有助于避免由默认配置或第三方库添加的不期望的控制台输出
    # 注意: 如果其他库确实需要它们自己的 handler，这可能会产生副作用
    if logger.hasHandlers():
        print(f"Root logger has handlers: {logger.handlers}, attempting to remove them.")
        logging.warning(f"Root logger has handlers: {logger.handlers}, attempting to remove them.")
        for handler in logger.handlers[:]: # Iterate over a copy
            try:
                # handler.close() # 关闭 handler
                logger.removeHandler(handler)
            except Exception as e:
                print(f"Error removing handler {handler}: {e}")
                logging.error(f"Error removing handler {handler}: {e}")
    # ------------------------------------------------- #

    # 创建 FileHandler
    # 使用 mode='w' 确保每次启动时清空旧日志（如果这是期望行为）
    # 如果希望追加日志，请使用 mode='a'
    # 根据需求，这里初始时不清空，由程序逻辑控制清空
    try:
        file_handler = logging.FileHandler(LOG_FILE, mode='a', encoding='utf-8')
    except OSError as e:
        print(f"错误：无法打开日志文件 {LOG_FILE} 进行写入: {e}")
        # 可以选择记录到控制台或退出
        logging.basicConfig(level=LOG_LEVEL, format=LOG_FORMAT, datefmt=LOG_DATE_FORMAT)
        logging.error(f"无法初始化文件日志处理器: {e}")
        app_state.log_file_handler = None # 标记 handler 创建失败
        return

    # 创建并设置 Formatter
    formatter = logging.Formatter(LOG_FORMAT, datefmt=LOG_DATE_FORMAT)
    file_handler.setFormatter(formatter)

    # 将 handler 存储在 AppState 中，以便动态添加/移除
    app_state.log_file_handler = file_handler

    # 初始时不将 handler 添加到 logger，由状态机控制
    # logger.addHandler(file_handler)

    # （可选）添加一个 StreamHandler 以同时输出到控制台
    # stream_handler = logging.StreamHandler()
    # stream_handler.setFormatter(formatter)
    # logger.addHandler(stream_handler)

    print(f"日志系统初始化完成。日志将根据程序逻辑动态写入到: {LOG_FILE}")
    logging.info("程序启动 - 日志系统初始化") # 这条会被记录，因为 logger level 已设置

def main():
    # 1. 创建 AppState 实例
    app_state = AppState()
    app_state.prev_time = time.time() # 初始化 FPS 计时器

    # 2. 配置日志系统
    setup_logging(app_state)
    # --- 日志设置结束 ---

    # 3. 初始化异步任务管理器
    async_task_manager.initialize_task_manager()
    logging.info("异步任务管理器已启动")

    # 4. 加载配置
    config_manager.load_config(app_state) # 会根据配置文件或用户输入设置初始模式

    # 5. 初始化摄像头
    if not camera_manager.initialize_camera(app_state):
        logging.error("摄像头初始化失败，程序退出。")
        print("摄像头初始化失败，程序退出。")
        # 关闭异步任务管理器
        async_task_manager.shutdown_task_manager()
        return # 无法继续

    # 6. 创建窗口并设置鼠标回调
    cv2.namedWindow(MAIN_WINDOW, cv2.WINDOW_NORMAL)
    ui_handler.setup_mouse_callback(MAIN_WINDOW, app_state) # 使用 ui_handler 设置回调

    # 7. 主循环
    while app_state.running:
        # 调用 ui_handler 处理当前帧的逻辑和 UI 更新
        ui_handler.process_ui_and_logic(app_state)

        # 主循环也检查退出键 'q'，以防模式处理函数未捕获
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
             logging.info("用户按下 'q' 退出程序。")
             app_state.running = False

        # 检查窗口是否被用户关闭
        try:
             if cv2.getWindowProperty(MAIN_WINDOW, cv2.WND_PROP_VISIBLE) < 1:
                  logging.info("检测到窗口关闭事件。")
                  print("检测到窗口关闭事件。")
                  app_state.running = False
        except cv2.error:
             logging.warning("窗口句柄无效 (可能已关闭)，程序退出。")
             print("窗口句柄无效，程序退出。")
             app_state.running = False


    # 8. 清理资源
    logging.info("正在关闭程序...")
    print("正在关闭程序...")

    # 关闭异步任务管理器
    async_task_manager.shutdown_task_manager()
    logging.info("异步任务管理器已关闭")

    camera_manager.release_camera(app_state) # 释放摄像头
    cv2.destroyAllWindows()

    # 关闭日志处理器
    if app_state.log_file_handler:
        logger = logging.getLogger()
        try:
            # 确保在关闭前移除 handler
            logger.removeHandler(app_state.log_file_handler)
            app_state.log_file_handler.close()
            logging.info("日志文件处理器已关闭。")
        except Exception as e:
            # 在关闭时可能发生错误，记录但不中断退出
            print(f"关闭日志处理器时出错: {e}")

    logging.info("程序正常结束")
    print("程序已退出。")

if __name__ == "__main__":
    main()
