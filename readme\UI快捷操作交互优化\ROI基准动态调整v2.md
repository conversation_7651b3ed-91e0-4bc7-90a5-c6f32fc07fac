# ROI基准动态调整方案 v2.0

## 📋 项目概述

### 核心问题
当前视觉检测系统使用固定坐标标记LED和数码管ROI，产品摆放位置的微小偏差（±5-20像素）会导致检测异常。

### 解决目标
通过**简化的基准点对齐系统**，实现ROI位置的自动校正，确保检测精度不受产品摆放位置偏差影响。

### 技术方案
- **双基准点定位**：选择产品上2个稳定特征点
- **模板匹配对齐**：实时计算位置偏移量
- **批量ROI调整**：同步调整所有ROI坐标
- **智能降级处理**：匹配失败时使用原始坐标

---

## 🎯 核心功能设计

### ✅ 必要功能（v2.0实现）
- [x] **基准点选择**：校准时手动选择2个特征点
- [x] **模板提取**：自动截取30×30像素模板
- [x] **实时对齐**：检测时自动计算偏移并调整ROI
- [x] **配置持久化**：基准点数据保存到配置文件
- [x] **容错机制**：匹配失败时回退原始坐标

### 🔄 优化功能（后续版本）
- [ ] 基准点质量评估
- [ ] 搜索区域限制
- [ ] 旋转补偿
- [ ] 调试可视化

---

## 🏗️ 实施架构

### 1. 数据结构扩展

#### AppState类新增属性
```python
class AppState:
    def __init__(self):
        # 现有属性...
        
        # === 基准点对齐系统 ===
        # 基准点坐标和模板
        self.base_points = [None, None]  # 当前基准点位置
        self.base_templates = [None, None]  # 基准点模板图像
        self.original_base_points = [None, None]  # 校准时的原始基准点位置
        
        # 原始ROI坐标（用于计算偏移）
        self.original_led_rois = [None] * self.led_max_rois
        self.original_digit_rois = [None] * NUM_DIGITS
        self.original_digit_segment_rois = [[None] * NUM_SEGMENTS_PER_DIGIT for _ in range(NUM_DIGITS)]
        
        # 对齐控制参数
        self.alignment_enabled = True  # 是否启用动态对齐
        self.base_template_size = 30  # 模板尺寸
        self.base_match_threshold = 0.75  # 匹配阈值
        self.alignment_fail_count = 0  # 连续失败计数
        
        # 校准状态
        self.calib_base_point_index = 0  # 当前选择的基准点索引(0或1)
```

#### Constants常量扩展
```python
# 基准点校准状态
CALIB_STATE_BASE_POINTS_SELECT = 9  # 基准点选择状态

# 基准点参数
BASE_TEMPLATE_SIZE = 30
BASE_MATCH_THRESHOLD = 0.75
BASE_ALIGNMENT_TIMEOUT = 5  # 连续失败超时次数
```

### 2. 核心算法实现

#### A. 基准点模板提取
```python
def extract_base_template(frame, center_x, center_y, template_size=30):
    """从指定中心点提取模板图像"""
    half_size = template_size // 2
    x1 = max(0, center_x - half_size)
    y1 = max(0, center_y - half_size)
    x2 = min(frame.shape[1], center_x + half_size)
    y2 = min(frame.shape[0], center_y + half_size)
    
    if x2 <= x1 or y2 <= y1:
        return None
        
    template = frame[y1:y2, x1:x2].copy()
    return template
```

#### B. 基准点搜索匹配
```python
def find_base_points(frame, app_state):
    """在当前帧中搜索基准点位置"""
    if not app_state.base_templates[0] or not app_state.base_templates[1]:
        return False, []
    
    current_points = []
    
    for i in range(2):
        template = app_state.base_templates[i]
        
        # 模板匹配
        result = cv2.matchTemplate(frame, template, cv2.TM_CCOEFF_NORMED)
        _, max_val, _, max_loc = cv2.minMaxLoc(result)
        
        if max_val >= app_state.base_match_threshold:
            # 计算基准点中心坐标
            center_x = max_loc[0] + app_state.base_template_size // 2
            center_y = max_loc[1] + app_state.base_template_size // 2
            current_points.append((center_x, center_y))
        else:
            return False, []
    
    # 验证基准点间距离合理性
    if len(current_points) == 2:
        current_dist = np.sqrt((current_points[0][0] - current_points[1][0])**2 + 
                              (current_points[0][1] - current_points[1][1])**2)
        original_dist = np.sqrt((app_state.original_base_points[0][0] - app_state.original_base_points[1][0])**2 +
                               (app_state.original_base_points[0][1] - app_state.original_base_points[1][1])**2)
        
        # 距离变化超过20%认为异常
        if abs(current_dist - original_dist) > original_dist * 0.2:
            return False, []
    
    return True, current_points
```

#### C. ROI动态对齐
```python
def auto_align_rois(app_state, frame):
    """自动对齐所有ROI到当前产品位置"""
    if not app_state.alignment_enabled:
        return True
    
    # 搜索基准点
    success, current_points = find_base_points(frame, app_state)
    
    if not success:
        app_state.alignment_fail_count += 1
        if app_state.alignment_fail_count >= BASE_ALIGNMENT_TIMEOUT:
            logging.warning(f"基准点对齐连续失败{app_state.alignment_fail_count}次")
        return False
    
    # 重置失败计数
    app_state.alignment_fail_count = 0
    
    # 计算平移偏移量（使用平均偏移）
    dx1 = current_points[0][0] - app_state.original_base_points[0][0]
    dy1 = current_points[0][1] - app_state.original_base_points[0][1]
    dx2 = current_points[1][0] - app_state.original_base_points[1][0] 
    dy2 = current_points[1][1] - app_state.original_base_points[1][1]
    
    dx = int(round((dx1 + dx2) / 2.0))
    dy = int(round((dy1 + dy2) / 2.0))
    
    logging.debug(f"ROI自动对齐: 偏移量=({dx}, {dy})")
    
    # 调整所有ROI
    adjust_all_rois(app_state, dx, dy)
    
    return True

def adjust_all_rois(app_state, dx, dy):
    """批量调整所有ROI坐标"""
    # 调整LED ROI
    for i in range(app_state.led_max_rois):
        if app_state.original_led_rois[i]:
            ox, oy, ow, oh = app_state.original_led_rois[i]
            app_state.led_rois[i] = (ox + dx, oy + dy, ow, oh)
    
    # 调整数码管整体ROI
    for i in range(NUM_DIGITS):
        if app_state.original_digit_rois[i]:
            ox, oy, ow, oh = app_state.original_digit_rois[i]
            app_state.digit_rois[i] = (ox + dx, oy + dy, ow, oh)
    
    # 调整数码管段ROI
    for i in range(NUM_DIGITS):
        for j in range(NUM_SEGMENTS_PER_DIGIT):
            if app_state.original_digit_segment_rois[i][j]:
                ox, oy, ow, oh = app_state.original_digit_segment_rois[i][j]
                app_state.digit_segment_rois[i][j] = (ox + dx, oy + dy, ow, oh)
```

### 3. UI交互集成

#### 校准流程修改
```python
# 在CALIB_STATE_START中添加基准点选择选项
if app_state.current_calib_state == CALIB_STATE_START:
    app_state.prompt_message = "校准模式: 'B' 基准点 | 'L' LED | 'D' 数码管 | 'S' 保存退出"
    
    if key == ord('b'):
        app_state.current_calib_state = CALIB_STATE_BASE_POINTS_SELECT
        app_state.calib_base_point_index = 0

# 基准点选择状态处理
elif app_state.current_calib_state == CALIB_STATE_BASE_POINTS_SELECT:
    idx = app_state.calib_base_point_index
    
    if idx < 2:
        app_state.prompt_message = f"点击选择基准点 {idx + 1}/2 (选择明显特征点) | 'Esc' 跳过"
        app_state.status_message = f"基准点校准: {idx}/2 已完成"
    else:
        app_state.prompt_message = "'Enter' 继续LED校准 | 'R' 重选基准点 | 'Esc' 跳过"
        app_state.status_message = "基准点选择完成"
    
    # 鼠标点击选择基准点
    if event == cv2.EVENT_LBUTTONDOWN and idx < 2:
        template = extract_base_template(frame, x, y, app_state.base_template_size)
        if template is not None:
            app_state.base_points[idx] = (x, y)
            app_state.base_templates[idx] = template
            print(f"基准点 {idx + 1} 已选择: ({x}, {y})")
            app_state.calib_base_point_index += 1
    
    elif key == 13 and idx >= 2:  # Enter继续
        app_state.original_base_points = app_state.base_points.copy()
        app_state.current_calib_state = CALIB_STATE_LED_ROI_SELECT
        print("基准点校准完成，开始LED ROI选择")
    
    elif key == ord('r'):  # 重选
        app_state.calib_base_point_index = 0
        app_state.base_points = [None, None]
        app_state.base_templates = [None, None]
    
    elif key == 27:  # Esc跳过
        app_state.alignment_enabled = False
        app_state.current_calib_state = CALIB_STATE_LED_ROI_SELECT
        print("跳过基准点校准，使用固定坐标模式")
```

#### 检测模式集成
```python
def _run_detection_mode(app_state: AppState):
    # 读取原始帧
    ret, frame = app_state.cap.read()
    if not ret or frame is None:
        return
    
    original_frame = frame.copy()
    app_state.display_frame = frame.copy()
    
    # ⭐ ROI自动对齐
    alignment_success = auto_align_rois(app_state, original_frame)
    if not alignment_success and app_state.alignment_enabled:
        app_state.status_message += f" | 基准点对齐失败({app_state.alignment_fail_count})"
    
    # 继续原有检测流程
    if app_state.led_max_rois > 0:
        led_detector.detect_led_status(original_frame, app_state)
    
    if any(roi for roi in app_state.digit_rois):
        digit_detector.detect_digit_status(original_frame, app_state)
```

---

## 📝 实施计划

### 第一阶段：基础架构 (2小时)
- [ ] 扩展AppState类，添加基准点相关属性
- [ ] 更新constants.py，添加新常量
- [ ] 创建基准点管理函数框架

### 第二阶段：核心算法 (3小时)
- [ ] 实现基准点模板提取函数
- [ ] 实现模板匹配搜索算法
- [ ] 实现ROI批量调整逻辑

### 第三阶段：UI集成 (2小时)
- [ ] 添加基准点选择交互逻辑
- [ ] 集成到校准流程
- [ ] 集成到检测模式

### 第四阶段：配置管理 (1小时)
- [ ] 扩展配置保存/加载功能
- [ ] 实现模板图像序列化
- [ ] 添加原始ROI坐标持久化

### 第五阶段：测试验证 (2小时)
- [ ] 功能集成测试
- [ ] 参数调优
- [ ] 边界情况处理

**总预计工时: 10小时**

---

## 🧪 验证测试

### 基本功能测试
1. **基准点选择**：能否正确选择和保存基准点
2. **模板匹配**：±15像素偏移下的匹配成功率
3. **ROI对齐**：对齐后的检测精度是否提升
4. **容错处理**：匹配失败时是否正确回退

### 性能指标
- 模板匹配延迟：< 10ms
- ROI调整精度：± 2-3像素
- 匹配成功率：> 90%（正常光照条件）

---

## ⚠️ 风险控制

### 主要风险
1. **基准点选择不当**：用户选择变化区域
2. **光照环境变化**：影响模板匹配精度
3. **性能影响**：每帧模板匹配的计算负担

### 应对策略
1. **用户指导**：提供基准点选择建议
2. **阈值调整**：支持动态调整匹配阈值
3. **优雅降级**：匹配失败时自动使用原始坐标
4. **可选启用**：支持禁用对齐功能

---

## 🎯 预期效果

### 解决的问题
- ✅ 产品摆放位置偏差导致的检测异常
- ✅ 手动重新校准的繁琐操作
- ✅ 系统对环境变化的敏感性

### 提升的指标
- 🎯 检测稳定性提升80%
- 🎯 位置容忍度：±20像素
- 🎯 减少重新校准频率90%

---

**文档版本**: v2.0  
**创建日期**: 2025-08-07  
**状态**: 待实施  
**重点**: 解决ROI位置偏差问题，简化实现复杂度
