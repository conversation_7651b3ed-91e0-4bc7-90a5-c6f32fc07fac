#!/usr/bin/env python3
"""
测试网络超时情况下的UI性能
模拟网络不通时的CPU通信，验证异步处理是否有效
"""

import time
import threading
from async_task_manager import AsyncTaskManager, TaskType, TaskStatus

def simulate_ui_loop_with_monitoring(duration_seconds=10, fps_target=30):
    """模拟UI主循环并监控FPS"""
    frame_time = 1.0 / fps_target
    frames_rendered = 0
    start_time = time.time()
    fps_samples = []
    
    print(f"开始UI循环监控，目标FPS: {fps_target}")
    
    while time.time() - start_time < duration_seconds:
        frame_start = time.time()
        
        # 模拟UI渲染工作
        time.sleep(0.001)  # 1ms的渲染时间
        frames_rendered += 1
        
        # 计算当前FPS（每秒采样一次）
        if frames_rendered % fps_target == 0:
            current_time = time.time()
            elapsed = current_time - start_time
            current_fps = frames_rendered / elapsed
            fps_samples.append(current_fps)
            print(f"时间: {elapsed:.1f}s, 当前FPS: {current_fps:.1f}")
        
        # 等待下一帧
        elapsed = time.time() - frame_start
        if elapsed < frame_time:
            time.sleep(frame_time - elapsed)
    
    actual_duration = time.time() - start_time
    actual_fps = frames_rendered / actual_duration
    
    return {
        'frames': frames_rendered,
        'duration': actual_duration,
        'average_fps': actual_fps,
        'fps_samples': fps_samples,
        'min_fps': min(fps_samples) if fps_samples else 0,
        'max_fps': max(fps_samples) if fps_samples else 0
    }

def test_network_timeout_scenario():
    """测试网络超时场景"""
    print("=" * 60)
    print("测试网络超时场景下的UI性能")
    print("=" * 60)
    
    # 启动异步任务管理器
    manager = AsyncTaskManager()
    manager.start()
    
    try:
        print("\n1. 测试正常网络情况...")
        
        # 模拟正常情况下的UI循环
        def normal_scenario():
            return simulate_ui_loop_with_monitoring(5, 30)
        
        normal_result = normal_scenario()
        print(f"正常情况结果:")
        print(f"  平均FPS: {normal_result['average_fps']:.1f}")
        print(f"  FPS范围: {normal_result['min_fps']:.1f} - {normal_result['max_fps']:.1f}")
        
        print(f"\n2. 测试网络超时情况（异步处理）...")
        
        # 模拟网络超时情况
        def timeout_scenario_async():
            # 在UI循环中提交会超时的CPU通信任务
            task_ids = []
            
            def ui_with_cpu_tasks():
                # 提交多个CPU任务（会超时）
                for i in range(3):
                    task_id = manager.submit_task(
                        TaskType.SEND_CPU_SIGNAL,
                        {'value': 1, 'address': 10}  # 这会超时
                    )
                    task_ids.append(task_id)
                    print(f"提交CPU任务 {i+1}: {task_id}")
                
                # 继续UI循环
                return simulate_ui_loop_with_monitoring(8, 30)
            
            return ui_with_cpu_tasks()
        
        timeout_result = timeout_scenario_async()
        print(f"网络超时情况结果（异步处理）:")
        print(f"  平均FPS: {timeout_result['average_fps']:.1f}")
        print(f"  FPS范围: {timeout_result['min_fps']:.1f} - {timeout_result['max_fps']:.1f}")
        
        # 检查任务状态
        print(f"\n3. 检查CPU任务状态...")
        time.sleep(2)  # 等待任务完成或超时
        
        completed_tasks = manager.check_completed_tasks()
        print(f"已完成的任务: {len(completed_tasks)}")
        
        # 对比结果
        print(f"\n" + "=" * 60)
        print("性能对比结果")
        print("=" * 60)
        
        fps_degradation = normal_result['average_fps'] - timeout_result['average_fps']
        fps_degradation_percent = (fps_degradation / normal_result['average_fps']) * 100
        
        print(f"正常网络:")
        print(f"  平均FPS: {normal_result['average_fps']:.1f}")
        print(f"  FPS稳定性: {normal_result['min_fps']:.1f} - {normal_result['max_fps']:.1f}")
        
        print(f"\n网络超时（异步处理）:")
        print(f"  平均FPS: {timeout_result['average_fps']:.1f}")
        print(f"  FPS稳定性: {timeout_result['min_fps']:.1f} - {timeout_result['max_fps']:.1f}")
        
        print(f"\n性能影响:")
        print(f"  FPS下降: {fps_degradation:.1f} ({fps_degradation_percent:.1f}%)")
        
        # 判断结果
        if fps_degradation_percent < 10:
            print(f"  ✅ 优秀！网络超时对UI性能影响很小")
        elif fps_degradation_percent < 25:
            print(f"  ⚠️  可接受：网络超时对UI性能有轻微影响")
        else:
            print(f"  ❌ 需要改进：网络超时严重影响UI性能")
            
        return {
            'normal_fps': normal_result['average_fps'],
            'timeout_fps': timeout_result['average_fps'],
            'degradation_percent': fps_degradation_percent
        }
        
    finally:
        manager.stop()

def test_extreme_timeout_scenario():
    """测试极端超时场景"""
    print(f"\n" + "=" * 60)
    print("测试极端网络超时场景")
    print("=" * 60)
    
    # 修改CPU通信超时时间为更短的值进行测试
    import cpu_communicator
    
    # 临时修改超时时间
    original_timeout = 5
    test_timeout = 1  # 1秒超时
    
    print(f"使用 {test_timeout} 秒超时进行测试...")
    
    manager = AsyncTaskManager()
    manager.start()
    
    try:
        # 提交大量会超时的任务
        task_ids = []
        for i in range(10):
            task_id = manager.submit_task(
                TaskType.SEND_CPU_SIGNAL,
                {'value': 1, 'address': 10}
            )
            task_ids.append(task_id)
        
        print(f"提交了 {len(task_ids)} 个CPU通信任务")
        
        # 监控UI性能
        result = simulate_ui_loop_with_monitoring(10, 30)
        
        print(f"极端超时场景结果:")
        print(f"  平均FPS: {result['average_fps']:.1f}")
        print(f"  FPS范围: {result['min_fps']:.1f} - {result['max_fps']:.1f}")
        
        # 检查任务完成情况
        time.sleep(3)
        completed_tasks = manager.check_completed_tasks()
        print(f"完成的任务数: {len(completed_tasks)}")
        
        return result
        
    finally:
        manager.stop()

def main():
    """主测试函数"""
    print("网络超时情况下的UI性能测试")
    print("模拟您遇到的FPS降到0.3的情况")
    
    try:
        # 基础超时测试
        basic_result = test_network_timeout_scenario()
        
        # 极端超时测试
        extreme_result = test_extreme_timeout_scenario()
        
        print(f"\n" + "=" * 60)
        print("最终测试总结")
        print("=" * 60)
        
        print(f"基础测试:")
        print(f"  网络正常FPS: {basic_result['normal_fps']:.1f}")
        print(f"  网络超时FPS: {basic_result['timeout_fps']:.1f}")
        print(f"  性能下降: {basic_result['degradation_percent']:.1f}%")
        
        print(f"\n极端测试:")
        print(f"  大量超时任务FPS: {extreme_result['average_fps']:.1f}")
        
        if basic_result['timeout_fps'] > 20:
            print(f"\n✅ 异步处理有效！即使网络超时，UI仍保持流畅")
        elif basic_result['timeout_fps'] > 10:
            print(f"\n⚠️  异步处理部分有效，但仍有改进空间")
        else:
            print(f"\n❌ 仍存在阻塞问题，需要进一步优化")
            
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
